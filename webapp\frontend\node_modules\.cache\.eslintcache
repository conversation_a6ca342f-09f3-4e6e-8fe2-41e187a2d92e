[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "93", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "97", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "107", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "108", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "109", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "110", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "111"}, {"size": 557, "mtime": 1746952718482, "results": "112", "hashOfConfig": "113"}, {"size": 2728, "mtime": 1748879437279, "results": "114", "hashOfConfig": "113"}, {"size": 996, "mtime": 1746970152489, "results": "115", "hashOfConfig": "113"}, {"size": 10788, "mtime": 1746864244183, "results": "116", "hashOfConfig": "113"}, {"size": 21191, "mtime": 1748751093271, "results": "117", "hashOfConfig": "113"}, {"size": 7269, "mtime": 1748879407880, "results": "118", "hashOfConfig": "113"}, {"size": 2216, "mtime": 1746640055487, "results": "119", "hashOfConfig": "113"}, {"size": 7394, "mtime": 1748034003517, "results": "120", "hashOfConfig": "113"}, {"size": 6749, "mtime": 1746282201800, "results": "121", "hashOfConfig": "113"}, {"size": 18812, "mtime": 1748879486791, "results": "122", "hashOfConfig": "113"}, {"size": 2535, "mtime": 1746647873596, "results": "123", "hashOfConfig": "113"}, {"size": 2050, "mtime": 1746647945415, "results": "124", "hashOfConfig": "113"}, {"size": 700, "mtime": 1747545501078, "results": "125", "hashOfConfig": "113"}, {"size": 17518, "mtime": 1748664526035, "results": "126", "hashOfConfig": "113"}, {"size": 3028, "mtime": 1748816305304, "results": "127", "hashOfConfig": "113"}, {"size": 2070, "mtime": 1748815989656, "results": "128", "hashOfConfig": "113"}, {"size": 1630, "mtime": 1746336079554, "results": "129", "hashOfConfig": "113"}, {"size": 1909, "mtime": 1748722592098, "results": "130", "hashOfConfig": "113"}, {"size": 57173, "mtime": 1748975926991, "results": "131", "hashOfConfig": "113"}, {"size": 324, "mtime": 1748757444974, "results": "132", "hashOfConfig": "113"}, {"size": 9068, "mtime": 1746856425683, "results": "133", "hashOfConfig": "113"}, {"size": 2210, "mtime": 1747432283057, "results": "134", "hashOfConfig": "113"}, {"size": 4494, "mtime": 1748121063631, "results": "135", "hashOfConfig": "113"}, {"size": 38195, "mtime": 1748813903832, "results": "136", "hashOfConfig": "113"}, {"size": 3337, "mtime": 1748816346924, "results": "137", "hashOfConfig": "113"}, {"size": 2958, "mtime": 1748816316425, "results": "138", "hashOfConfig": "113"}, {"size": 3507, "mtime": 1748816326922, "results": "139", "hashOfConfig": "113"}, {"size": 3345, "mtime": 1748816357091, "results": "140", "hashOfConfig": "113"}, {"size": 3340, "mtime": 1748816336281, "results": "141", "hashOfConfig": "113"}, {"size": 2975, "mtime": 1747554796402, "results": "142", "hashOfConfig": "113"}, {"size": 3429, "mtime": 1747721794176, "results": "143", "hashOfConfig": "113"}, {"size": 3109, "mtime": 1747824114392, "results": "144", "hashOfConfig": "113"}, {"size": 2929, "mtime": 1747655572696, "results": "145", "hashOfConfig": "113"}, {"size": 3302, "mtime": 1748000902435, "results": "146", "hashOfConfig": "113"}, {"size": 6125, "mtime": 1748705680231, "results": "147", "hashOfConfig": "113"}, {"size": 5880, "mtime": 1748121404574, "results": "148", "hashOfConfig": "113"}, {"size": 3889, "mtime": 1748664890350, "results": "149", "hashOfConfig": "113"}, {"size": 4720, "mtime": 1746771178920, "results": "150", "hashOfConfig": "113"}, {"size": 7121, "mtime": 1746281148395, "results": "151", "hashOfConfig": "113"}, {"size": 7958, "mtime": 1746280443400, "results": "152", "hashOfConfig": "113"}, {"size": 6259, "mtime": 1746965906057, "results": "153", "hashOfConfig": "113"}, {"size": 4215, "mtime": 1746278746358, "results": "154", "hashOfConfig": "113"}, {"size": 1273, "mtime": 1746809069006, "results": "155", "hashOfConfig": "113"}, {"size": 14270, "mtime": 1748371983481, "results": "156", "hashOfConfig": "113"}, {"size": 2752, "mtime": 1747022186740, "results": "157", "hashOfConfig": "113"}, {"size": 1072, "mtime": 1746637929350, "results": "158", "hashOfConfig": "113"}, {"size": 6745, "mtime": 1747545492454, "results": "159", "hashOfConfig": "113"}, {"size": 41680, "mtime": 1748816669877, "results": "160", "hashOfConfig": "113"}, {"size": 500, "mtime": 1748722841235, "results": "161", "hashOfConfig": "113"}, {"size": 47844, "mtime": 1748876421138, "results": "162", "hashOfConfig": "113"}, {"size": 38669, "mtime": 1748199713253, "results": "163", "hashOfConfig": "113"}, {"size": 1947, "mtime": 1748120984640, "results": "164", "hashOfConfig": "113"}, {"size": 54895, "mtime": 1748370360136, "results": "165", "hashOfConfig": "113"}, {"size": 14635, "mtime": 1748666301849, "results": "166", "hashOfConfig": "113"}, {"size": 13824, "mtime": 1748979601299, "results": "167", "hashOfConfig": "113"}, {"size": 11835, "mtime": 1748920731807, "results": "168", "hashOfConfig": "113"}, {"size": 2211, "mtime": 1748686293878, "results": "169", "hashOfConfig": "113"}, {"size": 9215, "mtime": 1748668814050, "results": "170", "hashOfConfig": "113"}, {"size": 10993, "mtime": 1747154871546, "results": "171", "hashOfConfig": "113"}, {"size": 12150, "mtime": 1748205557322, "results": "172", "hashOfConfig": "113"}, {"size": 24566, "mtime": 1748691444876, "results": "173", "hashOfConfig": "113"}, {"size": 7032, "mtime": 1748069273238, "results": "174", "hashOfConfig": "113"}, {"size": 8589, "mtime": 1748207111023, "results": "175", "hashOfConfig": "113"}, {"size": 9979, "mtime": 1748069243848, "results": "176", "hashOfConfig": "113"}, {"size": 10821, "mtime": 1748069202177, "results": "177", "hashOfConfig": "113"}, {"size": 36555, "mtime": 1747684003188, "results": "178", "hashOfConfig": "113"}, {"size": 9483, "mtime": 1747194869458, "results": "179", "hashOfConfig": "113"}, {"size": 16178, "mtime": 1748875708468, "results": "180", "hashOfConfig": "113"}, {"size": 48588, "mtime": 1747948123233, "results": "181", "hashOfConfig": "113"}, {"size": 92270, "mtime": 1748123070273, "results": "182", "hashOfConfig": "113"}, {"size": 522, "mtime": 1747022186711, "results": "183", "hashOfConfig": "113"}, {"size": 10251, "mtime": 1748805459799, "results": "184", "hashOfConfig": "113"}, {"size": 7740, "mtime": 1748881233022, "results": "185", "hashOfConfig": "113"}, {"size": 1703, "mtime": 1746972529152, "results": "186", "hashOfConfig": "113"}, {"size": 19892, "mtime": 1747554544219, "results": "187", "hashOfConfig": "113"}, {"size": 12050, "mtime": 1747547543421, "results": "188", "hashOfConfig": "113"}, {"size": 1686, "mtime": 1746946499500, "results": "189", "hashOfConfig": "113"}, {"size": 5145, "mtime": 1746914029633, "results": "190", "hashOfConfig": "113"}, {"size": 10721, "mtime": 1748751269815, "results": "191", "hashOfConfig": "113"}, {"size": 22179, "mtime": 1747432554979, "results": "192", "hashOfConfig": "113"}, {"size": 2574, "mtime": 1748920719208, "results": "193", "hashOfConfig": "113"}, {"size": 4094, "mtime": 1748161663641, "results": "194", "hashOfConfig": "113"}, {"size": 5273, "mtime": 1747946737459, "results": "195", "hashOfConfig": "113"}, {"size": 4346, "mtime": 1747491472989, "results": "196", "hashOfConfig": "113"}, {"size": 15647, "mtime": 1748899398456, "results": "197", "hashOfConfig": "113"}, {"size": 6742, "mtime": 1748751174061, "results": "198", "hashOfConfig": "113"}, {"size": 6529, "mtime": 1748664406267, "results": "199", "hashOfConfig": "113"}, {"size": 15764, "mtime": 1748877145346, "results": "200", "hashOfConfig": "113"}, {"size": 6899, "mtime": 1748877131332, "results": "201", "hashOfConfig": "113"}, {"size": 5536, "mtime": 1748670096009, "results": "202", "hashOfConfig": "113"}, {"size": 5457, "mtime": 1748666884369, "results": "203", "hashOfConfig": "113"}, {"size": 5605, "mtime": 1748666925194, "results": "204", "hashOfConfig": "113"}, {"size": 77752, "mtime": 1748878387989, "results": "205", "hashOfConfig": "113"}, {"size": 2807, "mtime": 1748705699971, "results": "206", "hashOfConfig": "113"}, {"size": 23591, "mtime": 1748881382254, "results": "207", "hashOfConfig": "113"}, {"size": 3708, "mtime": 1748705727900, "results": "208", "hashOfConfig": "113"}, {"size": 10270, "mtime": 1748724524628, "results": "209", "hashOfConfig": "113"}, {"size": 8247, "mtime": 1748756088995, "results": "210", "hashOfConfig": "113"}, {"size": 11038, "mtime": 1748756003708, "results": "211", "hashOfConfig": "113"}, {"size": 15055, "mtime": 1748755908778, "results": "212", "hashOfConfig": "113"}, {"size": 16415, "mtime": 1748755956687, "results": "213", "hashOfConfig": "113"}, {"size": 3434, "mtime": 1748755857115, "results": "214", "hashOfConfig": "113"}, {"size": 3483, "mtime": 1748755829302, "results": "215", "hashOfConfig": "113"}, {"size": 3508, "mtime": 1748755842942, "results": "216", "hashOfConfig": "113"}, {"size": 956, "mtime": 1748878396989, "results": "217", "hashOfConfig": "113"}, {"size": 13327, "mtime": 1748881322351, "results": "218", "hashOfConfig": "113"}, {"size": 15536, "mtime": 1748899852696, "results": "219", "hashOfConfig": "113"}, {"size": 3613, "mtime": 1748921268108, "results": "220", "hashOfConfig": "113"}, {"size": 1153, "mtime": 1748921279608, "results": "221", "hashOfConfig": "113"}, {"size": 6579, "mtime": 1748922219011, "results": "222", "hashOfConfig": "113"}, {"size": 8976, "mtime": 1748922249445, "results": "223", "hashOfConfig": "113"}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["557"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["558", "559", "560", "561", "562"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["563", "564", "565", "566"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["567", "568", "569", "570", "571", "572", "573", "574", "575", "576"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["577"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["578", "579", "580", "581", "582", "583", "584", "585", "586", "587"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["603"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["604", "605", "606", "607", "608", "609", "610", "611"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["631"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["632"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["633", "634", "635"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["636", "637", "638", "639"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["640", "641"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["642", "643"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["644", "645", "646", "647"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["648", "649", "650", "651"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["652"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["653", "654", "655", "656", "657", "658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["660", "661", "662"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["663", "664", "665", "666", "667", "668", "669"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["670", "671"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["672", "673"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["674", "675"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["676", "677"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["678", "679", "680", "681", "682", "683", "684", "685"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["700", "701", "702", "703", "704", "705", "706"], ["707"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["731", "732"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["733", "734", "735"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["736", "737"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["749"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["772"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["773"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["774"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["786"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["787", "788", "789", "790"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["831", "832"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["833"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["834", "835"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["856", "857", "858", "859", "860", "861", "862", "863"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["864"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["865", "866", "867"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["868", "869"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["870"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["871"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["872"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["873"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["874", "875", "876", "877", "878", "879"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["880"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["881", "882", "883", "884", "885", "886", "887", "888"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["889", "890", "891", "892", "893", "894", "895"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["896", "897", "898", "899", "900", "901", "902", "903", "904"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["905", "906", "907", "908", "909", "910", "911", "912", "913"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["914", "915", "916"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["917", "918", "919"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["920", "921", "922", "923"], [], {"ruleId": "924", "severity": 1, "message": "925", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 14}, {"ruleId": "928", "severity": 1, "message": "929", "line": 97, "column": 71, "nodeType": "930", "messageId": "931", "endLine": 97, "endColumn": 100}, {"ruleId": "928", "severity": 1, "message": "929", "line": 98, "column": 70, "nodeType": "930", "messageId": "931", "endLine": 98, "endColumn": 99}, {"ruleId": "928", "severity": 1, "message": "929", "line": 99, "column": 67, "nodeType": "930", "messageId": "931", "endLine": 99, "endColumn": 96}, {"ruleId": "928", "severity": 1, "message": "929", "line": 100, "column": 76, "nodeType": "930", "messageId": "931", "endLine": 100, "endColumn": 105}, {"ruleId": "928", "severity": 1, "message": "929", "line": 101, "column": 71, "nodeType": "930", "messageId": "931", "endLine": 101, "endColumn": 100}, {"ruleId": "932", "severity": 1, "message": "933", "line": 78, "column": 11, "nodeType": "934", "messageId": "935", "endLine": 78, "endColumn": 115}, {"ruleId": "932", "severity": 1, "message": "933", "line": 80, "column": 11, "nodeType": "934", "messageId": "935", "endLine": 80, "endColumn": 107}, {"ruleId": "932", "severity": 1, "message": "933", "line": 86, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 86, "endColumn": 105}, {"ruleId": "932", "severity": 1, "message": "933", "line": 89, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 89, "endColumn": 41}, {"ruleId": "924", "severity": 1, "message": "936", "line": 13, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 13, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "937", "line": 20, "column": 25, "nodeType": "926", "messageId": "927", "endLine": 20, "endColumn": 34}, {"ruleId": "924", "severity": 1, "message": "938", "line": 21, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 21, "endColumn": 35}, {"ruleId": "924", "severity": 1, "message": "939", "line": 22, "column": 12, "nodeType": "926", "messageId": "927", "endLine": 22, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "940", "line": 23, "column": 18, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 28}, {"ruleId": "924", "severity": 1, "message": "941", "line": 57, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 57, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "942", "line": 58, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 58, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "943", "line": 59, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 59, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "944", "line": 60, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 60, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "945", "line": 69, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 69, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "946", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "947", "line": 2, "column": 27, "nodeType": "926", "messageId": "927", "endLine": 2, "endColumn": 31}, {"ruleId": "924", "severity": 1, "message": "948", "line": 2, "column": 33, "nodeType": "926", "messageId": "927", "endLine": 2, "endColumn": 37}, {"ruleId": "924", "severity": 1, "message": "949", "line": 2, "column": 39, "nodeType": "926", "messageId": "927", "endLine": 2, "endColumn": 50}, {"ruleId": "924", "severity": 1, "message": "950", "line": 2, "column": 52, "nodeType": "926", "messageId": "927", "endLine": 2, "endColumn": 66}, {"ruleId": "924", "severity": 1, "message": "936", "line": 2, "column": 68, "nodeType": "926", "messageId": "927", "endLine": 2, "endColumn": 74}, {"ruleId": "924", "severity": 1, "message": "937", "line": 5, "column": 25, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 34}, {"ruleId": "924", "severity": 1, "message": "938", "line": 6, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 35}, {"ruleId": "924", "severity": 1, "message": "939", "line": 7, "column": 12, "nodeType": "926", "messageId": "927", "endLine": 7, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "940", "line": 8, "column": 18, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 28}, {"ruleId": "924", "severity": 1, "message": "951", "line": 43, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 43, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "925", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "947", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "952", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "953", "line": 15, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 15, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "954", "line": 16, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 16, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "955", "line": 17, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "956", "line": 18, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 18, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "957", "line": 19, "column": 13, "nodeType": "926", "messageId": "927", "endLine": 19, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "958", "line": 20, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 20, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "959", "line": 25, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 25, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "960", "line": 28, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 28, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "961", "line": 48, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 48, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "962", "line": 53, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 53, "endColumn": 20}, {"ruleId": "924", "severity": 1, "message": "963", "line": 11, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "966", "line": 7, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 7, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "967", "line": 12, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "953", "line": 13, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 13, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "968", "line": 17, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "960", "line": 21, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 21, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "966", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "970", "line": 14, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "971", "line": 26, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 16}, {"ruleId": "924", "severity": 1, "message": "972", "line": 30, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "973", "line": 32, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 32, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "974", "line": 33, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "975", "line": 41, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 41, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "976", "line": 49, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 49, "endColumn": 16}, {"ruleId": "924", "severity": 1, "message": "960", "line": 57, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 57, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "977", "line": 59, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 59, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "969", "line": 61, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 61, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "978", "line": 234, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 234, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "979", "line": 242, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 242, "endColumn": 28}, {"ruleId": "924", "severity": 1, "message": "980", "line": 243, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 243, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "981", "line": 243, "column": 25, "nodeType": "926", "messageId": "927", "endLine": 243, "endColumn": 41}, {"ruleId": "982", "severity": 1, "message": "983", "line": 603, "column": 6, "nodeType": "984", "endLine": 603, "endColumn": 15, "suggestions": "985"}, {"ruleId": "924", "severity": 1, "message": "986", "line": 648, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 648, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "987", "line": 1, "column": 27, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 36}, {"ruleId": "924", "severity": 1, "message": "988", "line": 49, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 49, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "952", "line": 15, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 15, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "967", "line": 39, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 39, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "989", "line": 43, "column": 16, "nodeType": "926", "messageId": "927", "endLine": 43, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "990", "line": 48, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 48, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "990", "line": 37, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 37, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "990", "line": 52, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 52, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "990", "line": 48, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 48, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "990", "line": 48, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 48, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "969", "line": 27, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 27, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "991", "line": 6, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "953", "line": 14, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "960", "line": 23, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "969", "line": 30, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "990", "line": 33, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "992", "line": 38, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 38, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "960", "line": 20, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 20, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "969", "line": 27, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 27, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "992", "line": 35, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 35, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "991", "line": 6, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "953", "line": 14, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "960", "line": 23, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "969", "line": 30, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "990", "line": 33, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "992", "line": 38, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 38, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "960", "line": 24, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 24, "endColumn": 26}, {"ruleId": "982", "severity": 1, "message": "993", "line": 53, "column": 6, "nodeType": "984", "endLine": 53, "endColumn": 18, "suggestions": "994"}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "952", "line": 14, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "997", "line": 28, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 28, "endColumn": 18}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "925", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "998", "line": 23, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "999", "line": 24, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 24, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "954", "line": 46, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 46, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "1000", "line": 47, "column": 12, "nodeType": "926", "messageId": "927", "endLine": 47, "endColumn": 21}, {"ruleId": "982", "severity": 1, "message": "1001", "line": 134, "column": 6, "nodeType": "984", "endLine": 134, "endColumn": 18, "suggestions": "1002"}, {"ruleId": "924", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "925", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "998", "line": 23, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "999", "line": 24, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 24, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "952", "line": 25, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 25, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "966", "line": 29, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 29, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "956", "line": 39, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 39, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "954", "line": 43, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 43, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "1003", "line": 44, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 44, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "1004", "line": 50, "column": 69, "nodeType": "926", "messageId": "927", "endLine": 50, "endColumn": 76}, {"ruleId": "924", "severity": 1, "message": "1005", "line": 79, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 79, "endColumn": 26}, {"ruleId": "982", "severity": 1, "message": "1006", "line": 179, "column": 6, "nodeType": "984", "endLine": 179, "endColumn": 8, "suggestions": "1007"}, {"ruleId": "924", "severity": 1, "message": "1008", "line": 697, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 697, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "1009", "line": 20, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 20, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "1010", "line": 21, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 21, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1011", "line": 22, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 22, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "947", "line": 23, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 23, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "1012", "line": 26, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 26, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1013", "line": 69, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 69, "endColumn": 22}, {"ruleId": "1014", "severity": 1, "message": "1015", "line": 466, "column": 9, "nodeType": "1016", "messageId": "1017", "endLine": 469, "endColumn": 10}, {"ruleId": "982", "severity": 1, "message": "1018", "line": 95, "column": 6, "nodeType": "984", "endLine": 95, "endColumn": 21, "suggestions": "1019", "suppressions": "1020"}, {"ruleId": "932", "severity": 1, "message": "933", "line": 260, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 264, "endColumn": 11}, {"ruleId": "932", "severity": 1, "message": "933", "line": 274, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 274, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 278, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 278, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 333, "column": 11, "nodeType": "934", "messageId": "935", "endLine": 338, "endColumn": 13}, {"ruleId": "932", "severity": 1, "message": "933", "line": 435, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 439, "endColumn": 11}, {"ruleId": "932", "severity": 1, "message": "933", "line": 451, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 451, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 668, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 668, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 677, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 677, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 681, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 681, "endColumn": 54}, {"ruleId": "924", "severity": 1, "message": "1021", "line": 755, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 755, "endColumn": 22}, {"ruleId": "932", "severity": 1, "message": "933", "line": 775, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 779, "endColumn": 11}, {"ruleId": "932", "severity": 1, "message": "933", "line": 794, "column": 11, "nodeType": "934", "messageId": "935", "endLine": 798, "endColumn": 13}, {"ruleId": "932", "severity": 1, "message": "933", "line": 801, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 804, "endColumn": 11}, {"ruleId": "932", "severity": 1, "message": "933", "line": 810, "column": 11, "nodeType": "934", "messageId": "935", "endLine": 814, "endColumn": 13}, {"ruleId": "932", "severity": 1, "message": "933", "line": 817, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 820, "endColumn": 11}, {"ruleId": "932", "severity": 1, "message": "933", "line": 885, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 889, "endColumn": 11}, {"ruleId": "1022", "severity": 1, "message": "1023", "line": 955, "column": 3, "nodeType": "1024", "messageId": "1025", "endLine": 955, "endColumn": 29}, {"ruleId": "1022", "severity": 1, "message": "1026", "line": 1143, "column": 3, "nodeType": "1024", "messageId": "1025", "endLine": 1143, "endColumn": 23}, {"ruleId": "1022", "severity": 1, "message": "1027", "line": 1238, "column": 3, "nodeType": "1024", "messageId": "1025", "endLine": 1238, "endColumn": 20}, {"ruleId": "932", "severity": 1, "message": "933", "line": 1287, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 1287, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 1317, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 1317, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 1370, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 1370, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 1412, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 1412, "endColumn": 163}, {"ruleId": "924", "severity": 1, "message": "1028", "line": 6, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "952", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "1029", "line": 6, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "975", "line": 7, "column": 15, "nodeType": "926", "messageId": "927", "endLine": 7, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "1030", "line": 8, "column": 16, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1031", "line": 3, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 3, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1032", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 6}, {"ruleId": "924", "severity": 1, "message": "1033", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "1034", "line": 6, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1035", "line": 7, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 7, "endColumn": 6}, {"ruleId": "924", "severity": 1, "message": "1036", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1037", "line": 36, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 36, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "1038", "line": 50, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 50, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1039", "line": 64, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 64, "endColumn": 20}, {"ruleId": "924", "severity": 1, "message": "1040", "line": 88, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 88, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "1041", "line": 104, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 104, "endColumn": 30}, {"ruleId": "924", "severity": 1, "message": "1042", "line": 3, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 3, "endColumn": 12}, {"ruleId": "924", "severity": 1, "message": "1034", "line": 3, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 3, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1035", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 6}, {"ruleId": "924", "severity": 1, "message": "1043", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "1044", "line": 6, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 6, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "1045", "line": 7, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 7, "endColumn": 16}, {"ruleId": "924", "severity": 1, "message": "1046", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "1036", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1047", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "1031", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1032", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 6}, {"ruleId": "924", "severity": 1, "message": "1033", "line": 13, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 13, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "1048", "line": 14, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 16}, {"ruleId": "924", "severity": 1, "message": "1049", "line": 15, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 15, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "1042", "line": 16, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 16, "endColumn": 12}, {"ruleId": "924", "severity": 1, "message": "1050", "line": 18, "column": 40, "nodeType": "926", "messageId": "927", "endLine": 18, "endColumn": 44}, {"ruleId": "924", "severity": 1, "message": "1051", "line": 47, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 47, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1052", "line": 64, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 64, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1053", "line": 71, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 71, "endColumn": 20}, {"ruleId": "924", "severity": 1, "message": "1040", "line": 79, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 79, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "1041", "line": 95, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 95, "endColumn": 30}, {"ruleId": "924", "severity": 1, "message": "1054", "line": 299, "column": 27, "nodeType": "926", "messageId": "927", "endLine": 299, "endColumn": 37}, {"ruleId": "924", "severity": 1, "message": "1055", "line": 300, "column": 27, "nodeType": "926", "messageId": "927", "endLine": 300, "endColumn": 36}, {"ruleId": "924", "severity": 1, "message": "965", "line": 3, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 3, "endColumn": 8}, {"ruleId": "982", "severity": 1, "message": "1056", "line": 54, "column": 6, "nodeType": "984", "endLine": 54, "endColumn": 34, "suggestions": "1057"}, {"ruleId": "924", "severity": 1, "message": "1058", "line": 25, "column": 13, "nodeType": "926", "messageId": "927", "endLine": 25, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "1059", "line": 33, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1060", "line": 34, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 34, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1061", "line": 35, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 35, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "1062", "line": 36, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 36, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "1063", "line": 37, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 37, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1064", "line": 41, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 41, "endColumn": 20}, {"ruleId": "924", "severity": 1, "message": "1065", "line": 43, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 43, "endColumn": 34}, {"ruleId": "924", "severity": 1, "message": "1066", "line": 69, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 69, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1067", "line": 69, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 69, "endColumn": 29}, {"ruleId": "982", "severity": 1, "message": "1068", "line": 88, "column": 6, "nodeType": "984", "endLine": 88, "endColumn": 18, "suggestions": "1069"}, {"ruleId": "982", "severity": 1, "message": "1070", "line": 448, "column": 6, "nodeType": "984", "endLine": 448, "endColumn": 28, "suggestions": "1071"}, {"ruleId": "924", "severity": 1, "message": "1072", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 12}, {"ruleId": "924", "severity": 1, "message": "1073", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "1074", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1075", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 15}, {"ruleId": "982", "severity": 1, "message": "1056", "line": 46, "column": 6, "nodeType": "984", "endLine": 46, "endColumn": 18, "suggestions": "1076"}, {"ruleId": "924", "severity": 1, "message": "1077", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1009", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "1010", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1011", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1075", "line": 33, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1078", "line": 35, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 35, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "1003", "line": 42, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 42, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "1059", "line": 52, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 52, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1060", "line": 53, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 53, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1061", "line": 54, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 54, "endColumn": 22}, {"ruleId": "924", "severity": 1, "message": "1062", "line": 55, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 55, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "1063", "line": 56, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 56, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1079", "line": 57, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 57, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1080", "line": 58, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 58, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1081", "line": 59, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 59, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "977", "line": 72, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 72, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1082", "line": 79, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 79, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "1083", "line": 79, "column": 25, "nodeType": "926", "messageId": "927", "endLine": 79, "endColumn": 41}, {"ruleId": "924", "severity": 1, "message": "1084", "line": 80, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 80, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "1085", "line": 85, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 85, "endColumn": 26}, {"ruleId": "982", "severity": 1, "message": "1056", "line": 105, "column": 6, "nodeType": "984", "endLine": 105, "endColumn": 18, "suggestions": "1086"}, {"ruleId": "982", "severity": 1, "message": "1087", "line": 112, "column": 6, "nodeType": "984", "endLine": 112, "endColumn": 20, "suggestions": "1088"}, {"ruleId": "982", "severity": 1, "message": "1089", "line": 127, "column": 6, "nodeType": "984", "endLine": 127, "endColumn": 34, "suggestions": "1090"}, {"ruleId": "924", "severity": 1, "message": "1091", "line": 283, "column": 13, "nodeType": "926", "messageId": "927", "endLine": 283, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1012", "line": 17, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1075", "line": 34, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 34, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1078", "line": 35, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 35, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "1092", "line": 39, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 39, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1059", "line": 51, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 51, "endColumn": 15}, {"ruleId": "924", "severity": 1, "message": "1060", "line": 52, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 52, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1062", "line": 54, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 54, "endColumn": 21}, {"ruleId": "924", "severity": 1, "message": "1063", "line": 55, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 55, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1065", "line": 62, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 62, "endColumn": 34}, {"ruleId": "924", "severity": 1, "message": "1093", "line": 105, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 105, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "1094", "line": 105, "column": 28, "nodeType": "926", "messageId": "927", "endLine": 105, "endColumn": 47}, {"ruleId": "982", "severity": 1, "message": "1087", "line": 145, "column": 6, "nodeType": "984", "endLine": 145, "endColumn": 18, "suggestions": "1095"}, {"ruleId": "924", "severity": 1, "message": "1096", "line": 701, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 701, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1097", "line": 1311, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 1311, "endColumn": 28}, {"ruleId": "924", "severity": 1, "message": "1098", "line": 1316, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 1316, "endColumn": 30}, {"ruleId": "924", "severity": 1, "message": "1099", "line": 1883, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 1883, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1100", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "995", "line": 1, "column": 8, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "996", "line": 5, "column": 7, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1101", "line": 83, "column": 13, "nodeType": "926", "messageId": "927", "endLine": 83, "endColumn": 21}, {"ruleId": "932", "severity": 1, "message": "933", "line": 109, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 109, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 123, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 123, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 127, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 127, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 212, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 212, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 226, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 226, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 230, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 230, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 271, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 271, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 280, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 280, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 284, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 284, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 320, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 320, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 324, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 324, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 360, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 360, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 369, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 369, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 373, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 373, "endColumn": 54}, {"ruleId": "932", "severity": 1, "message": "933", "line": 450, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 450, "endColumn": 163}, {"ruleId": "932", "severity": 1, "message": "933", "line": 459, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 459, "endColumn": 70}, {"ruleId": "932", "severity": 1, "message": "933", "line": 463, "column": 9, "nodeType": "934", "messageId": "935", "endLine": 463, "endColumn": 54}, {"ruleId": "924", "severity": 1, "message": "1102", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "955", "line": 27, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 27, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "971", "line": 30, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1061", "line": 34, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 34, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "1066", "line": 49, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 49, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1067", "line": 49, "column": 19, "nodeType": "926", "messageId": "927", "endLine": 49, "endColumn": 29}, {"ruleId": "982", "severity": 1, "message": "1056", "line": 64, "column": 6, "nodeType": "984", "endLine": 64, "endColumn": 32, "suggestions": "1103"}, {"ruleId": "924", "severity": 1, "message": "1104", "line": 270, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 270, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "1105", "line": 17, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "1011", "line": 16, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 16, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1010", "line": 17, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1009", "line": 19, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 19, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "971", "line": 14, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 14, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1106", "line": 43, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 43, "endColumn": 26}, {"ruleId": "924", "severity": 1, "message": "966", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "1107", "line": 33, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 29}, {"ruleId": "924", "severity": 1, "message": "1108", "line": 3, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 3, "endColumn": 6}, {"ruleId": "924", "severity": 1, "message": "952", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "1109", "line": 20, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 20, "endColumn": 19}, {"ruleId": "982", "severity": 1, "message": "1110", "line": 136, "column": 6, "nodeType": "984", "endLine": 136, "endColumn": 18, "suggestions": "1111"}, {"ruleId": "982", "severity": 1, "message": "1112", "line": 141, "column": 6, "nodeType": "984", "endLine": 141, "endColumn": 52, "suggestions": "1113"}, {"ruleId": "982", "severity": 1, "message": "1114", "line": 146, "column": 6, "nodeType": "984", "endLine": 146, "endColumn": 62, "suggestions": "1115"}, {"ruleId": "982", "severity": 1, "message": "1116", "line": 151, "column": 6, "nodeType": "984", "endLine": 151, "endColumn": 28, "suggestions": "1117"}, {"ruleId": "982", "severity": 1, "message": "1118", "line": 160, "column": 6, "nodeType": "984", "endLine": 160, "endColumn": 39, "suggestions": "1119"}, {"ruleId": "982", "severity": 1, "message": "1120", "line": 68, "column": 6, "nodeType": "984", "endLine": 68, "endColumn": 18, "suggestions": "1121"}, {"ruleId": "924", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 8, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 9, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "991", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1074", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 11}, {"ruleId": "924", "severity": 1, "message": "1122", "line": 21, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 21, "endColumn": 31}, {"ruleId": "924", "severity": 1, "message": "940", "line": 24, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 24, "endColumn": 27}, {"ruleId": "982", "severity": 1, "message": "1123", "line": 180, "column": 6, "nodeType": "984", "endLine": 180, "endColumn": 25, "suggestions": "1124"}, {"ruleId": "928", "severity": 1, "message": "1125", "line": 243, "column": 15, "nodeType": "930", "messageId": "931", "endLine": 248, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "999", "line": 15, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 15, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "952", "line": 16, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 16, "endColumn": 10}, {"ruleId": "924", "severity": 1, "message": "956", "line": 28, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 28, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "957", "line": 29, "column": 13, "nodeType": "926", "messageId": "927", "endLine": 29, "endColumn": 23}, {"ruleId": "924", "severity": 1, "message": "1126", "line": 39, "column": 34, "nodeType": "926", "messageId": "927", "endLine": 39, "endColumn": 59}, {"ruleId": "924", "severity": 1, "message": "1066", "line": 41, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 41, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "987", "line": 1, "column": 27, "nodeType": "926", "messageId": "927", "endLine": 1, "endColumn": 36}, {"ruleId": "924", "severity": 1, "message": "948", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "964", "line": 12, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 12, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "1105", "line": 27, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 27, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "955", "line": 30, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1127", "line": 33, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 33, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "940", "line": 34, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 34, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "967", "line": 35, "column": 14, "nodeType": "926", "messageId": "927", "endLine": 35, "endColumn": 25}, {"ruleId": "924", "severity": 1, "message": "948", "line": 10, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 10, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 11, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 11, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1105", "line": 27, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 27, "endColumn": 8}, {"ruleId": "924", "severity": 1, "message": "1128", "line": 28, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 28, "endColumn": 12}, {"ruleId": "924", "severity": 1, "message": "1129", "line": 29, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 29, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "1130", "line": 30, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 30, "endColumn": 19}, {"ruleId": "924", "severity": 1, "message": "955", "line": 34, "column": 10, "nodeType": "926", "messageId": "927", "endLine": 34, "endColumn": 17}, {"ruleId": "924", "severity": 1, "message": "1131", "line": 37, "column": 17, "nodeType": "926", "messageId": "927", "endLine": 37, "endColumn": 31}, {"ruleId": "982", "severity": 1, "message": "1132", "line": 98, "column": 6, "nodeType": "984", "endLine": 98, "endColumn": 24, "suggestions": "1133"}, {"ruleId": "924", "severity": 1, "message": "948", "line": 4, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 4, "endColumn": 7}, {"ruleId": "924", "severity": 1, "message": "949", "line": 5, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 5, "endColumn": 14}, {"ruleId": "982", "severity": 1, "message": "1134", "line": 66, "column": 6, "nodeType": "984", "endLine": 66, "endColumn": 25, "suggestions": "1135"}, {"ruleId": "924", "severity": 1, "message": "1136", "line": 192, "column": 9, "nodeType": "926", "messageId": "927", "endLine": 192, "endColumn": 27}, {"ruleId": "924", "severity": 1, "message": "1137", "line": 229, "column": 11, "nodeType": "926", "messageId": "927", "endLine": 229, "endColumn": 24}, {"ruleId": "982", "severity": 1, "message": "1138", "line": 385, "column": 6, "nodeType": "984", "endLine": 385, "endColumn": 58, "suggestions": "1139"}, {"ruleId": "924", "severity": 1, "message": "1077", "line": 15, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 15, "endColumn": 14}, {"ruleId": "924", "severity": 1, "message": "1009", "line": 16, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 16, "endColumn": 13}, {"ruleId": "924", "severity": 1, "message": "1010", "line": 17, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 17, "endColumn": 9}, {"ruleId": "924", "severity": 1, "message": "1011", "line": 18, "column": 3, "nodeType": "926", "messageId": "927", "endLine": 18, "endColumn": 11}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1140"], "'getAllSelectedCavi' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1141"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1142"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1143"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1144"], ["1145"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1146"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1147"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1148"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["1149"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1150"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1151"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1152"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1153"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1154"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1155"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1156"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1157"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1158"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1159"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1160"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1161"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1162"], "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1163"], "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1164"], {"desc": "1165", "fix": "1166"}, {"desc": "1167", "fix": "1168"}, {"desc": "1169", "fix": "1170"}, {"desc": "1171", "fix": "1172"}, {"desc": "1173", "fix": "1174"}, {"kind": "1175", "justification": "1176"}, {"desc": "1177", "fix": "1178"}, {"desc": "1179", "fix": "1180"}, {"desc": "1181", "fix": "1182"}, {"desc": "1183", "fix": "1184"}, {"desc": "1183", "fix": "1185"}, {"desc": "1186", "fix": "1187"}, {"desc": "1188", "fix": "1189"}, {"desc": "1190", "fix": "1191"}, {"desc": "1192", "fix": "1193"}, {"desc": "1194", "fix": "1195"}, {"desc": "1196", "fix": "1197"}, {"desc": "1198", "fix": "1199"}, {"desc": "1200", "fix": "1201"}, {"desc": "1202", "fix": "1203"}, {"desc": "1204", "fix": "1205"}, {"desc": "1206", "fix": "1207"}, {"desc": "1208", "fix": "1209"}, {"desc": "1210", "fix": "1211"}, {"desc": "1212", "fix": "1213"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1214", "text": "1215"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1216", "text": "1217"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1218", "text": "1219"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1222", "text": "1223"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1224", "text": "1225"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1226", "text": "1227"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1228", "text": "1229"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1230", "text": "1231"}, {"range": "1232", "text": "1231"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1233", "text": "1234"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1235", "text": "1236"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1237", "text": "1238"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1239", "text": "1240"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1241", "text": "1242"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1243", "text": "1244"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1245", "text": "1246"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1247", "text": "1248"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1249", "text": "1250"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1251", "text": "1252"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1253", "text": "1254"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1255", "text": "1256"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1257", "text": "1258"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1259", "text": "1260"}, [24225, 24234], "[caviAttivi, caviSpare, error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [6583, 6585], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3608, 3620], "[cantiereId, loadInitialData]", [3705, 3751], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3835, 3891], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3997, 4019], "[calculateStatistics, cavi, certificazioni]", [4241, 4274], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1648, 1660], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]", [1523, 1542], "[loadCaviDisponibili, open, tipoComanda]", [11142, 11194], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]"]