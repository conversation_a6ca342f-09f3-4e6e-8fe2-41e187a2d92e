import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip, TableRow, TableCell, Checkbox, IconButton, Button } from '@mui/material';
import {
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Clear as ClearIcon,
  Straighten as RulerIcon,
  Settings as SettingsIcon,
  PlayArrow as StartIcon
} from '@mui/icons-material';
import FilterableTable from '../common/FilterableTable';
import SmartCaviFilter from './SmartCaviFilter';
import ContextMenu from '../common/ContextMenu';
import useContextMenu from '../../hooks/useContextMenu';
import { formatDate } from '../../utils/dateUtils';

/**
 * Componente per visualizzare la lista dei cavi con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista dei cavi da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche
 * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi
 * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati
 * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione
 * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale
 * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale
 * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato
 */
const CaviFilterableTable = ({
  cavi = [],
  loading = false,
  onFilteredDataChange = null,
  revisioneCorrente = null,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange = null,
  contextMenuItems = [],
  onContextMenuAction = null,
  onStatusAction = null
}) => {
  const [filteredCavi, setFilteredCavi] = useState(cavi);
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);

  // Hook per il menu contestuale
  const { contextMenu, handleContextMenu, closeContextMenu } = useContextMenu();

  // Aggiorna i dati filtrati quando cambiano i cavi
  useEffect(() => {
    setFilteredCavi(cavi);
    setSmartFilteredCavi(cavi);
  }, [cavi]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Gestisce il cambio dei dati dal filtro intelligente
  const handleSmartFilterChange = (data) => {
    console.log('CaviFilterableTable - Smart filter change:', {
      originalCount: cavi.length,
      filteredCount: data.length,
      filteredIds: data.map(c => c.id_cavo)
    });
    setSmartFilteredCavi(data);
    // Il filtro intelligente ha la priorità sui filtri Excel-like
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Gestisce la selezione di un singolo cavo
  const handleCavoToggle = (cavoId) => {
    if (!selectionEnabled || !onSelectionChange) return;

    const isSelected = selectedCavi.includes(cavoId);
    let newSelection;

    if (isSelected) {
      // Rimuovi dalla selezione
      newSelection = selectedCavi.filter(id => id !== cavoId);
      console.log(`Cavo ${cavoId} deselezionato`);
    } else {
      // Aggiungi alla selezione
      newSelection = [...selectedCavi, cavoId];
      console.log(`Cavo ${cavoId} selezionato`);
    }

    onSelectionChange(newSelection);

    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)
    // Potresti aggiungere qui un piccolo toast o animazione
  };

  // Seleziona tutti i cavi visibili (filtrati)
  const handleSelectAll = () => {
    if (!selectionEnabled || !onSelectionChange) return;

    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);
    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));

    if (allSelected) {
      // Deseleziona tutti i cavi visibili
      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));
      onSelectionChange(newSelection);
    } else {
      // Seleziona tutti i cavi visibili
      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];
      onSelectionChange(newSelection);
    }
  };

  // Deseleziona tutti i cavi
  const handleClearSelection = () => {
    if (!selectionEnabled || !onSelectionChange) return;
    onSelectionChange([]);
  };



  // Definizione delle colonne
  const columns = [
    // Colonna di selezione (solo se abilitata)
    ...(selectionEnabled ? [{
      field: 'selection',
      headerName: '',
      disableFilter: true,
      disableSort: true,
      width: 50,
      align: 'center',
      headerStyle: { width: '50px', padding: '4px' },
      cellStyle: { width: '50px', padding: '4px', textAlign: 'center' },
      renderHeader: () => {
        const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);
        const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));
        const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));

        return (
          <Checkbox
            checked={allSelected}
            indeterminate={someSelected && !allSelected}
            onChange={handleSelectAll}
            size="small"
            title={allSelected ? "Deseleziona tutti" : "Seleziona tutti"}
          />
        );
      },
      renderCell: (row) => (
        <Checkbox
          checked={selectedCavi.includes(row.id_cavo)}
          onChange={() => handleCavoToggle(row.id_cavo)}
          size="small"
          onClick={(e) => e.stopPropagation()}
        />
      )
    }] : []),
    {
      field: 'id_cavo',
      headerName: 'ID Cavo',
      dataType: 'text',
      headerStyle: { fontWeight: 'bold' }
    },
    // Colonna Revisione rimossa e spostata nella tabella delle statistiche
    {
      field: 'sistema',
      headerName: 'Sistema',
      dataType: 'text'
    },
    {
      field: 'utility',
      headerName: 'Utility',
      dataType: 'text'
    },
    {
      field: 'tipologia',
      headerName: 'Tipologia',
      dataType: 'text'
    },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    {
      field: 'sezione',
      headerName: 'Formazione',
      dataType: 'text',
      align: 'right',
      cellStyle: { textAlign: 'right' }
    },
    {
      field: 'metri_teorici',
      headerName: 'Metri Teorici',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
    },
    {
      field: 'metratura_reale',
      headerName: 'Metri Reali',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'
    },
    {
      field: 'stato_installazione',
      headerName: 'Stato',
      dataType: 'text',
      renderCell: (row) => {
        // Determina colore, icona e azione in base allo stato
        let color = 'default';
        let icon = null;
        let actionLabel = '';
        let actionType = '';

        if (row.stato_installazione === 'INSTALLATO') {
          color = 'success';
          icon = <SettingsIcon fontSize="small" />;
          actionLabel = 'Modifica Bobina';
          actionType = 'modify_reel';
        } else if (row.stato_installazione === 'IN_CORSO') {
          color = 'warning';
          icon = <RulerIcon fontSize="small" />;
          actionLabel = 'Inserisci Metri Posati';
          actionType = 'insert_meters';
        } else if (row.stato_installazione === 'DA_INSTALLARE' || !row.stato_installazione) {
          color = 'error';
          icon = <StartIcon fontSize="small" />;
          actionLabel = 'Inserisci Metri Posati';
          actionType = 'insert_meters';
        }

        // Debug: verifica se onStatusAction è definito e se c'è un'azione per questo stato
        console.log('🔍 Rendering pulsante stato per cavo:', row.id_cavo, {
          stato: row.stato_installazione,
          onStatusAction: !!onStatusAction,
          actionType,
          actionLabel,
          hasAction: !!actionType
        });

        // Determina se il pulsante deve essere cliccabile
        const isClickable = onStatusAction && actionType && actionLabel;

        return (
          <Chip
            label={row.stato_installazione || 'N/D'}
            size="small"
            color={color}
            variant="outlined"
            icon={icon}
            onClick={isClickable ? (e) => {
              e.stopPropagation();
              console.log('🔥 CLICK su pulsante stato!', {
                cavoId: row.id_cavo,
                stato: row.stato_installazione,
                actionType,
                actionLabel
              });
              onStatusAction(row, actionType, actionLabel);
            } : undefined}
            sx={{
              cursor: isClickable ? 'pointer' : 'default',
              transition: 'all 0.2s ease',
              '&:hover': isClickable ? {
                transform: 'scale(1.05)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                backgroundColor: `${color}.light`
              } : {},
              // Aggiungi un bordo più marcato per i pulsanti cliccabili
              border: isClickable ? '2px solid currentColor' : '1px solid currentColor'
            }}
            title={isClickable ? actionLabel : 'Nessuna azione disponibile'}
          />
        );
      }
    },
    {
      field: 'id_bobina',
      headerName: 'Bobina',
      dataType: 'text',
      renderCell: (row) => {
        // Gestione differenziata per null e BOBINA_VUOTA
        if (row.id_bobina === null) {
          // Per cavi non posati (id_bobina è null)
          return '-';
        } else if (row.id_bobina === 'BOBINA_VUOTA') {
          // Per cavi posati senza bobina specifica
          return 'BOBINA VUOTA';
        } else if (!row.id_bobina) {
          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)
          return '-';
        }

        // Estrai solo il numero della bobina (parte dopo '_B')
        const match = row.id_bobina.match(/_B(.+)$/);
        return match ? match[1] : row.id_bobina;
      }
    },
    {
      field: 'timestamp',
      headerName: 'Data Modifica',
      dataType: 'date',
      renderCell: (row) => formatDate(row.timestamp)
    },
    {
      field: 'collegamenti',
      headerName: 'Collegamenti',
      dataType: 'number',
      align: 'center',
      cellStyle: { textAlign: 'center' },
      renderCell: (row) => {
        let color = 'default';
        if (row.collegamenti === 2) color = 'success';
        else if (row.collegamenti === 1) color = 'warning';
        else color = 'error';

        return (
          <Chip
            label={row.collegamenti}
            size="small"
            color={color}
            variant="outlined"
          />
        );
      }
    }
  ];

  // Renderizza una riga personalizzata
  const renderRow = (row, index) => {
    // Determina il colore di sfondo in base allo stato
    let bgColor = 'inherit';
    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';
    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';

    // Se la selezione è abilitata, evidenzia le righe selezionate
    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);
    if (isSelected) {
      bgColor = 'rgba(25, 118, 210, 0.25)'; // Blu più marcato per le righe selezionate
    }

    return (
      <TableRow
        key={index}
        selected={isSelected}
        hover
        onClick={selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined}
        onContextMenu={(e) => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined}
        sx={{
          backgroundColor: bgColor,
          cursor: selectionEnabled ? 'pointer' : 'default',
          transition: 'all 0.2s ease',
          border: isSelected ? '2px solid #1976d2' : '2px solid transparent',
          '&:hover': {
            backgroundColor: selectionEnabled
              ? (isSelected ? 'rgba(25, 118, 210, 0.35)' : 'rgba(25, 118, 210, 0.15)')
              : 'rgba(0, 0, 0, 0.04)',
            transform: selectionEnabled ? 'scale(1.01)' : 'none',
            boxShadow: selectionEnabled ? '0 2px 8px rgba(0,0,0,0.1)' : 'none'
          }
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            align={column.align || 'left'}
            sx={column.cellStyle}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    );
  };



  return (
    <Box>
      {/* Filtro intelligente */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
      />

      {/* Pannello di controllo selezione */}
      {selectionEnabled && (
        <Box sx={{
          mb: 2,
          p: 2,
          backgroundColor: selectedCavi.length > 0 ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.08)',
          borderRadius: 2,
          border: selectedCavi.length > 0 ? '2px solid rgba(25, 118, 210, 0.5)' : '2px solid rgba(25, 118, 210, 0.25)',
          transition: 'all 0.3s ease',
          boxShadow: selectedCavi.length > 0 ? '0 4px 12px rgba(25, 118, 210, 0.25)' : '0 2px 6px rgba(25, 118, 210, 0.1)'
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {selectedCavi.length > 0 && (
                <CheckBoxIcon color="primary" fontSize="small" />
              )}
              <Typography variant="body1" sx={{ fontWeight: 600, color: selectedCavi.length > 0 ? 'primary.main' : 'text.primary' }}>
                {selectedCavi.length > 0
                  ? `${selectedCavi.length} cavi selezionati`
                  : 'Modalità selezione attiva - Click sui cavi per selezionarli'
                }
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                size="small"
                onClick={handleSelectAll}
                disabled={filteredCavi.length === 0}
              >
                {filteredCavi.length > 0 && filteredCavi.every(cavo => selectedCavi.includes(cavo.id_cavo))
                  ? 'Deseleziona tutti'
                  : 'Seleziona tutti'
                }
              </Button>

              {selectedCavi.length > 0 && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<ClearIcon />}
                  onClick={handleClearSelection}
                  color="error"
                >
                  Cancella selezione
                </Button>
              )}
            </Box>
          </Box>
        </Box>
      )}

      {/* Tabella con filtri Excel-like sui dati già filtrati dal filtro intelligente */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        renderRow={renderRow}
      />

      {/* Menu contestuale */}
      <ContextMenu
        open={contextMenu.open}
        anchorPosition={contextMenu.anchorPosition}
        onClose={closeContextMenu}
        menuItems={typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems}
        contextData={contextMenu.contextData}
      />
    </Box>
  );
};

export default CaviFilterableTable;
